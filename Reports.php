<?php

    namespace App\Controllers;
    use App\Controllers\BaseController;
    use CodeIgniter\API\ResponseTrait;

    use App\Models\UserModel;
    use \Firebase\JWT\JWT;

class Reports extends BaseController

{

    use ResponseTrait;

    public function renewInsurance()   {
        
        $insurancesModel = new \App\Models\InsurancesModel();
        
        $jPFields = $this->request->getVar(); //to ARRAY;
        
        $reportModel = new \App\Models\ReportsModel();
        $GalBriutModel = new \App\Models\GalBriut();
        
        helper("site");
        
        //echo "<pre>";
        //print_r($jPFields);
//        die();
        
        //$galBriut = new \App\Models\GalBriut();
        //if( !$galBriut->checkUserType($jPFields)['isAdmin'] ) return $this->respond('NoAdmin', 401); //Unauthorized
        
        if( empty($jPFields['corporationId']) ) {return $this->respond('noCorporationId', 400);} //Bad request
        
        
        $data = $reportModel->getDataRenewInsurance($jPFields);
        
//        echo "<pre>";
//        print_r($data);
//        die();
        
        
        if(empty($data) ) {
            die("- NO DATA - ");
        }
        

            $csv = array();

            $csv[] = array (
                '1' => 'דו"ח: ',
                '2' => 'חידוש ביטוחים');
            
            $csv[] = array (
                '1' => 'תאגיד',
                '2' => $data[0]['corporationName']
            );
            
            $csv[] = array (
                '1' => 'חודש: ',
                '2' => $jPFields['month']);
            
            $csv[] = array (
                '1' => 'שנה: ',
                '2' => '20'.$jPFields['year']);
            
            $csv[] = array ('1' => '');
            
            $csv[] = array (
                '1' => 'זיהוי לקוח',
                '2' => 'שם המעסיק',
                '3' => 'עיר',
                '4' => 'שם מבוטח',
                '5' => 'סטטוס ביטוח',
                '6' => 'תחילת ביטוח',
                '7' => 'סיום ביטוח',
            );

            //echo "<pre>";
            //print_r($data);die();

            foreach ($data as $values) {

                $csv[] = array (
                    '1' => $values['seniorId'],
                    '2' => $values['seniorName'].' '.$values['seniorSurname'],
                    '3' => $values['seniorCity'],
                    '4' => $values['workerName'].' '.$values['workerSurname'],
                    '5' => $GalBriutModel->checkStatusName($values['status']),
                    '6' => changeDateFormat($values['insuranceStart'], "Y/m/d", $stFormatTo = "d.m.Y"),
                    '7' => changeDateFormat($values['insuranceEnd'], "Y/m/d", $stFormatTo = "d.m.Y"),
                );

            }

            $data = $csv;
            $month = date('M');  //date('m');
            $filename = $month.'_'.date('Y').'_'.rand(1,9999);

            header('Content-Encoding: UTF-8'); 
            header('Content-type: text/csv; charset=UTF-8');
            header('Content-Disposition: attachment; filename='.$filename.'.csv');
            header("Pragma: no-cache");
            header("Expires: 0");

            $handle = fopen('php://output', 'w');
            fwrite($handle, "\xEF\xBB\xBF");


            foreach ($data as $data_array) {
                fputcsv($handle, $data_array);
            }
                fclose($handle);
            exit;
        
        //$response = $this->siteModel->customRespJson($output);
        //return $this->respond($response, 200);
         
         
    }
    
    
    
    public function daysInsurance()   {
        
        $insurancesModel = new \App\Models\InsurancesModel();
        
        $jPFields = $this->request->getVar(); //to ARRAY;
        
        $reportModel = new \App\Models\ReportsModel();
        $GalBriutModel = new \App\Models\GalBriut();
        
        helper("site");
        
        //echo "<pre>";
        //print_r($jPFields);
        //die();
        
        //$galBriut = new \App\Models\GalBriut();
        //if( !$galBriut->checkUserType($jPFields)['isAdmin'] ) return $this->respond('NoAdmin', 401); //Unauthorized
        
        if( empty($jPFields['insuranceId']) ) {return $this->respond('noInsuranceId', 400);} //Bad request
        
        
        $data = $reportModel->getDataDaysInsurance($jPFields);
        
        //echo "<pre>";
        //print_r($data);
        //die();
        
        
        if(empty($data) ) {
            die("- NO DATA - ");
        }
        

            $csv = array();

            $csv[] = array (
                '1' => 'דו"ח: ',
                '2' => 'פעילים בחברות הביטוח');
            
            $csv[] = array (
                '1' => 'חברת ביטוח',
                '2' => $data[0]['insuranceFirmName']
            );
            
            $csv[] = array (
                '1' => 'חודש: ',
                '2' => $jPFields['month']);
            
            $csv[] = array (
                '1' => 'שנה: ',
                '2' => '20'.$jPFields['year']);
            
            $csv[] = array ('1' => '');
            
            $csv[] = array (
                '1' => 'זיהוי לקוח',
                '2' => 'שם המעסיק',
                '3' => 'שם מבוטח',                
                '4' => "מס' דרכון", 
                '5' => 'סטטוס ביטוח',
                '6' => "מס' פוליסה",                
                '7' => 'תחילת ביטוח',
                '8' => 'סיום ביטוח',
                '9' => 'סה"כ ימי ביטוח פעילים לחודש',
            );

            //echo "<pre>";
            //print_r($data);die();

            foreach ($data as $values) {

                $csv[] = array (
                    '1' => $values['seniorId'],
                    '2' => $values['seniorName'].' '.$values['seniorSurname'],
                    '3' => $values['workerName'].' '.$values['workerSurname'],
                    '4' => $values['workerPassport'],                    
                    '5' => $GalBriutModel->checkStatusName($values['status']),
                    '6' => $values['insurancePolisa'],    
                    '7' => changeDateFormat($values['insuranceStart'], "Y/m/d", $stFormatTo = "d.m.Y"),
                    '8' => changeDateFormat($values['insuranceEnd'], "Y/m/d", $stFormatTo = "d.m.Y"),
                    '9' => $values['daysMonthCount'], 
                );

            }

            $data = $csv;
            $month = date('M');  //date('m');
            $filename = $month.'_'.date('Y').'_'.rand(1,9999);

            header('Content-Encoding: UTF-8'); 
            header('Content-type: text/csv; charset=UTF-8');
            header('Content-Disposition: attachment; filename='.$filename.'.csv');
            header("Pragma: no-cache");
            header("Expires: 0");

            $handle = fopen('php://output', 'w');
            fwrite($handle, "\xEF\xBB\xBF");


            foreach ($data as $data_array) {
                fputcsv($handle, $data_array);
            }
                fclose($handle);
            exit;
        
        //$response = $this->siteModel->customRespJson($output);
        //return $this->respond($response, 200);
         
         
    }
    
    
    
    public function daysAgentMoneyInsurance()   {
        
        //die('-new-');
        
        $insurancesModel = new \App\Models\InsurancesModel();
        
        $jPFields = $this->request->getVar(); //to ARRAY;
        
        $reportModel = new \App\Models\ReportsModel();
        $GalBriutModel = new \App\Models\GalBriut();
        
        helper("site");
        
        //echo "<pre>";
        //print_r($jPFields);
        //die();
        
        //$galBriut = new \App\Models\GalBriut();
        //if( !$galBriut->checkUserType($jPFields)['isAdmin'] ) return $this->respond('NoAdmin', 401); //Unauthorized
        
        if( empty($jPFields['corporationId']) ) {return $this->respond('noCorporationId', 400);} //Bad request
        
        
        $data = $reportModel->getDataDaysAgentMoneyInsurance($jPFields);
        
        //echo "<pre>";
        //print_r($data);
        //die();
        
        
        if(empty($data) ) {
            die("- NO DATA - ");
        }
        

            $csv = array();

            $csv[] = array (
                '1' => 'דו"ח: ',
                '2' => 'התחשבנות סוכנים');
            
            $csv[] = array (
                '1' => 'תאגיד',
                '2' => $data[0]['corporationName']
            );
            
            $csv[] = array (
                '1' => 'חודש: ',
                '2' => $jPFields['month']);
            
            $csv[] = array (
                '1' => 'שנה: ',
                '2' => '20'.$jPFields['year']);
            
            $csv[] = array ('1' => '');
            
            $csv[] = array (
                '1' => 'זיהוי לקוח',
                '2' => 'שם המעסיק',
                '3' => 'שם מבוטח',                
                '4' => "מס' דרכון", 
                '5' => 'סטטוס ביטוח',
                //'6' => "מס' פוליסה",
                '7a' => 'תאריך ביצוע',
                '7' => 'תחילת ביטוח',
                '8' => 'סיום ביטוח',
                '9' => "מס' ימים",
                '10' => 'חברה מבטחת',
                '11' => 'תעריף לקוח', //תעריף שולם
                '12' => 'תעריף סוכן',
                //'13' => 'טוטל חברת ביטוח',
                //'14' => 'טוטל סוכן',
                '15' => 'שולם?',
                '16' => 'סה"כ לתשלום',
                '17' => 'עמלה',
            );

            //echo "<pre>";
            //print_r($data);die('-END');

            foreach ($data as $values) {
                
                if(!empty($values['seniorCustomerID'])) {
                    $seniorId = $values['seniorId'].' ('.$values['seniorCustomerID'].')';
                } else {
                    $seniorId = $values['seniorId'];
                }

                $csv[] = array (
                    '1' => $seniorId,
                    '2' => $values['seniorName'].' '.$values['seniorSurname'],
                    '3' => $values['workerName'].' '.$values['workerSurname'],
                    '4' => $values['workerPassport'],                    
                    '5' => $GalBriutModel->checkStatusName($values['status']),
                    //'6' => $values['insurancePolisa'],
                    '7a' => $values['dateRequestShow'],
                    '7' => $values['insuranceStartShow'],
                    '8' => $values['insuranceEndShow'],
                    '9' => $values['allDaysCount'], //$values['daysMonthCount'], 
                    '10' => $values['insuranceFirmName'],
                    '11' => number_format($values['rateInsuranceDay'],2, '.', ''),
                    '12' => number_format($values['rateBasis'],2, '.', ''),
                    //'13' => $values['totalBasis'],
                    //'14' => $values['payed'] ? $values['totalAgent'] : 0,
                    '15' => $values['payed'] ? 'כן' : 'לא',
                    '16' =>  number_format($values['totalAgent'] ,2, '.', ''),
                    '17' => $values['payed'] ? $values['efresh'] : 0,
                );

            }

            $data = $csv;
            $month = date('M');  //date('m');
            $filename = $month.'_'.date('Y').'_'.rand(1,9999);

            header('Content-Encoding: UTF-8'); 
            header('Content-type: text/csv; charset=UTF-8');
            header('Content-Disposition: attachment; filename='.$filename.'.csv');
            header("Pragma: no-cache");
            header("Expires: 0");

            $handle = fopen('php://output', 'w');
            fwrite($handle, "\xEF\xBB\xBF");


            foreach ($data as $data_array) {
                fputcsv($handle, $data_array);
            }
                fclose($handle);
            exit;
        
        //$response = $this->siteModel->customRespJson($output);
        //return $this->respond($response, 200);
         
         
    }
    
    
   
    
    public function usersActivity()   {
        
        $insurancesModel = new \App\Models\InsurancesModel();
        
        $jPFields = $this->request->getVar(); //to ARRAY;
        
        $reportModel = new \App\Models\ReportsModel();
        $GalBriutModel = new \App\Models\GalBriut();
        
        helper("site");
        
        //echo "<pre>";
        //print_r($jPFields);
//        die();
        
        //$galBriut = new \App\Models\GalBriut();
        //if( !$galBriut->checkUserType($jPFields)['isAdmin'] ) return $this->respond('NoAdmin', 401); //Unauthorized
        
        //if( empty($jPFields['corporationId']) ) {return $this->respond('noCorporationId', 400);} //Bad request
        
        
        $data = $reportModel->getDataUsersActivity($jPFields);
        
        //echo "<pre>";
        //print_r($data);
        //die();
        
        
        if(empty($data) ) {
            die("- NO DATA - ");
        }
        

            $csv = array();

            $csv[] = array (
                '1' => 'דו"ח: ',
                '2' => 'פעילות משתמשים');
            
//            $csv[] = array (
//                '1' => 'תאגיד',
//                '2' => $data[0]['corporationName']
//            );
            
            $csv[] = array (
                '1' => 'חודש: ',
                '2' => $jPFields['month']);
            
            $csv[] = array (
                '1' => 'שנה: ',
                '2' => '20'.$jPFields['year']);
            
            $csv[] = array ('1' => '');
            
            $csv[] = array (
                '1' => 'יוזר',
                '2' => 'תאגיד',
                '3' => "מס' מבוטחים פעילים",
                '4' => "מס' ימי ביטוח",
                '5' => "מס' חידושים שבוצעו",
                '6' => 'מספר שנותרו',
            );

            //echo "<pre>";
            //print_r($data);die();

            foreach ($data['users'] as $user) {

                $csv[] = array (
                    '1' => $data[$user['id']]['name'],
                    '2' => $data[$user['id']]['corpName'],
                    '3' => $data[$user['id']]['counters']['insureds'],
                    '4' => $data[$user['id']]['counters']['insuranceDaysMonthCount'],
                    '5' => $data[$user['id']]['counters']['reNew'],
                    '6' => $data[$user['id']]['counters']['mustReNew'],
                    
                );

            }

            $data = $csv;
            $month = date('M');  //date('m');
            $filename = $month.'_'.date('Y').'_'.rand(1,9999);

            header('Content-Encoding: UTF-8'); 
            header('Content-type: text/csv; charset=UTF-8');
            header('Content-Disposition: attachment; filename='.$filename.'.csv');
            header("Pragma: no-cache");
            header("Expires: 0");

            $handle = fopen('php://output', 'w');
            fwrite($handle, "\xEF\xBB\xBF");


            foreach ($data as $data_array) {
                fputcsv($handle, $data_array);
            }
                fclose($handle);
            exit;
        
        //$response = $this->siteModel->customRespJson($output);
        //return $this->respond($response, 200);
         
         
    }
     public function getAciveInsurences()   {
        $insurancesModel = new \App\Models\InsurancesModel();
        return "AciveInsurences";
     }
    
    public function corporationActivity()   {
        
        //die('test');
        
        $insurancesModel = new \App\Models\InsurancesModel();
        
        $jPFields = $this->request->getVar(); //to ARRAY;
        
        $reportModel = new \App\Models\ReportsModel();
        $GalBriutModel = new \App\Models\GalBriut();
        
        helper("site");
        
        //echo "<pre>";
        //print_r($jPFields);
        //die('corporationActivity');
        
        if( empty($jPFields['corporationId']) ) {return $this->respond('noCorporationId', 400);} //Bad request
        
        
        $data = $reportModel->getDataCorporationActivity($jPFields);
        
//        echo "<pre>";
//        print_r($data);
//        die();
        
        
        if(empty($data) ) {
            die("- NO DATA - ");
        }
        

            $csv = array();

            $csv[] = array (
                '1' => 'דו"ח: ',
                '2' => 'דו"ח הוראות ביצוע');
            
//            $csv[] = array (
//                '1' => 'תאגיד',
//                '2' => $data[0]['corporationName']
//            );
            
            $csv[] = array (
                '1' => 'חודש: ',
                '2' => $jPFields['month']);
            
            $csv[] = array (
                '1' => 'שנה: ',
                '2' => '20'.$jPFields['year']);
            
            $csv[] = array ('1' => '');
            
            $csv[] = array (
                '1' => 'זיהוי לקוח',
                '1a' => 'תאגיד',
                '2' => 'שם המעסיק',
                '3' => 'עיר',
                '4' => 'שם מבוטח',
                '5' => 'סטטוס ביטוח',
                '6' => 'תחילת ביטוח',
                '7' => 'סיום ביטוח',
                '8' => 'מספר חבר קופ"ח',
                '9' => 'חברה מבטחת',
                '10' => 'תעריף יומי',
                '11' => 'יוזר מבצע',
                '12' => 'תאריך שינוי',
                //'13' => 'מקט שינוי',
                //'13' => 'עידכון סטטוס',
//                '10' => 'עדכון לקוח',
//                '11' => 'עדכון עובד',
            );

            //echo "<pre>";
            //print_r($data);die();

            foreach ($data as $values) {

                $csv[] = array (
                    '1' => $values['seniorId'],
                    '1a' => $values['corporationName'],
                    '2' => $values['seniorName'].' '.$values['seniorSurname'],
                    '3' => $values['seniorCity'],
                    '4' => $values['workerName'].' '.$values['workerSurname'],
                    '5' => $GalBriutModel->checkStatusName($values['status']),
                    '6' => changeDateFormat($values['insuranceStart'], "Y/m/d", $stFormatTo = "d.m.Y"),
                    '7' => changeDateFormat($values['insuranceEnd'], "Y/m/d", $stFormatTo = "d.m.Y"),
                    '8' => $values['kupatHolim'],
                    '9' => $values['InsuranceFirmName'],
                    '10' => $values['rateInsuranceDay'],
                    '11' => isset($values['userName']) ? $values['userName'] : '',
                    '12' => changeDateFormat($values['created_at'], "Y-m-d H:i", $stFormatTo = "d.m.Y H:i"),
                    //'13' => $GalBriutModel->checkStatusName($values['newStatusId']),
                    
//                    '9' => $this->showDate($values,'created_at','updated_at'),
//                    '10' => $this->showDate($values,'seniorCreated','seniorUpdate'),
//                    '11' => $this->showDate($values,'workerCreated','workerUpdate'),
                );

            }

            $data = $csv;
            $month = date('M');  //date('m');
            $filename = $month.'_'.date('Y').'_'.rand(1,9999);

            header('Content-Encoding: UTF-8'); 
            header('Content-type: text/csv; charset=UTF-8');
            header('Content-Disposition: attachment; filename='.$filename.'.csv');
            header("Pragma: no-cache");
            header("Expires: 0");

            $handle = fopen('php://output', 'w');
            fwrite($handle, "\xEF\xBB\xBF");


            foreach ($data as $data_array) {
                fputcsv($handle, $data_array);
            }
                fclose($handle);
            exit;
        
        //$response = $this->siteModel->customRespJson($output);
        //return $this->respond($response, 200);
         
         
    }
    
    
    
    private function showDate($values,$checkField1, $checkField2) {
        
        if( isset($values[$checkField2]) && !empty($values[$checkField2]) ) {
            
            return changeDateFormat($values[$checkField2], "Y/m/d H:i", $stFormatTo = "d.m.Y H:i");
            
        } else if( isset($values[$checkField1]) && !empty($values[$checkField1]) ) {
            
            return changeDateFormat($values[$checkField1], "Y/m/d H:i", $stFormatTo = "d.m.Y H:i");
            
        } else {
            
            return '';
            
        }
        
        
        
    }


    
    public function corporationActivityFields()   {
         
        $siteModel = new \App\Models\SiteModel();
        $corporationsModel = new \App\Models\CorporationsModel();
        $galBriutModel = new \App\Models\GalBriut();
        
        $jPFields = $this->request->getJSON(true); //to ARRAY;
        
        //if( !$galBriutModel->checkUserType($jPFields)['isAdmin'] ) return $this->respond('NoAdmin', 401); //Unauthorized
        
        //if(empty($jPFields['userId'])) {return $this->respond('NoUserId', 400);} //Bad request
        
        $corporations = $corporationsModel->getCorporations($jPFields);
        
        $output['data']['corporations'] = isset($corporations['corporations']) ? $corporations['corporations'] : array();
        $output['data']['adminUsers'] = $galBriutModel->getAdminUsers();
        
        $response = $siteModel->customRespJson($output);
        return $this->respond($response, 200);
         
         
    }
    
    
    
}