<?php

namespace App\Models;

use CodeIgniter\Model;

use CodeIgniter\HTTP\IncomingRequest;
use \Firebase\JWT\JWT;
use \Firebase\JWT\Key;

class ReportsModel extends Model
{

    protected $helpers = ['site']; //loading helper  --> only first word! and helper("site");
    //https://onlinewebtutorblog.com/custom-helper-in-codeigniter-4/

    protected function initialize()
    {
        $this->allowedFields[] = 'middlename';
    }


    public function getDataRenewInsurance($jPFields = false)
    {

        //        [month] => 03
//        [year] => 23
//        [corporationId] => 16

        $insurancesModel = new \App\Models\InsurancesModel();

        $startDate = '20' . $jPFields['year'] . '-' . $jPFields['month'] . '-' . '1';
        $endDate = date("Y-m-t", strtotime($startDate));

        //die($endDate);

        $db = \Config\Database::connect();
        $mainTable = 'insurances';

        $builder = $db->table($mainTable);
        $builder->select(array(
            $mainTable . '.id',
            $mainTable . '.insuranceStart',
            $mainTable . '.insuranceEnd',
            $mainTable . '.status',
        ));

        $builder->where($mainTable . '.status', 2);
        //$builder->where($mainTable.'.seniorId', $jPFields['']);

        //----
        $join = array(
            'table1Field' => 'seniorId',
            'table2' => 'seniors', );

        $builder->join($join['table2'], $join['table2'] . '.id = ' . $mainTable . '.' . $join['table1Field'], 'left')
            ->select(array(
                $join['table2'] . '.id as seniorId',
                $join['table2'] . '.name as seniorName',
                $join['table2'] . '.surname as seniorSurname',
                $join['table2'] . '.city as seniorCity',
                //$join['table2'].'.corporationId as seniorCorporationId',
                //$join['table2'].'.corporationId as seniorCorporationId',
            ));

        //----

        $builder->where($join['table2'] . '.statusSenior', 'פעיל');

        //        //----

        $join['table2Field'] = 'corporationId';
        $join['table3'] = 'corporations';

        $builder->join($join['table3'], $join['table3'] . '.id = ' . $join['table2'] . '.' . $join['table2Field'], 'left')
            ->select(array(
                $join['table3'] . '.name as corporationName',
                //$join['table2'].'.surname as seniorSurname',
            ));

        //----

        //----

        $join['table3Field'] = 'workerId';
        $join['table4'] = 'workersSiud';

        $builder->join($join['table4'], $join['table4'] . '.id = ' . $mainTable . '.' . $join['table3Field'], 'left')
            ->select(array(
                $join['table4'] . '.name as workerName',
                $join['table4'] . '.surname as workerSurname',
            ));

        //----

        $builder->where($join['table2'] . '.corporationId', $jPFields['corporationId']);

        $builder->where($mainTable . '.insuranceEnd >= ', $startDate);
        $builder->where($mainTable . '.insuranceEnd <= ', $endDate);
        $builder->where($mainTable . '.dateCancel', NULL);

        $builder->orderBy($mainTable . '.insuranceEnd', 'ASC');

        $query = $builder->get();
        $results = $query->getResultArray();


        $addsPagTokef = $insurancesModel->addNoActiveInsurancesClientActive($jPFields);

        if (!empty($addsPagTokef)) {

            $startDate = '20' . $jPFields['year'] . '-' . $jPFields['month'] . '-' . '1';
            $endDate = date("Y-m-t", strtotime($startDate));

            $date1 = new \DateTime($startDate);
            $date2 = new \DateTime($endDate);

            $toMerge = array();
            foreach ($addsPagTokef as $addPagTokef) {

                $dateEnd = new \DateTime($addPagTokef['insuranceEnd']);

                if ($dateEnd >= $date1 && $dateEnd <= $date2) {

                    $toMerge[] = $addPagTokef;

                }

            }


            //echo "<pre>";
            //print_r($addsPagTokef);
            //die('- End -');

            $results = array_merge($results, $toMerge);

        }

        //echo "<pre>";
        //print_r($addsPagTokef); 
        //die();

        return $results;

    }


    public function getDataDaysInsurance($jPFields = false)
    {

        //        [month] => 03
//        [year] => 23
//        [insuranceId] => 2

        $startDate = '20' . $jPFields['year'] . '-' . $jPFields['month'] . '-' . '1';
        $endDate = date("Y-m-t", strtotime($startDate));

        //die($endDate);

        $db = \Config\Database::connect();
        $mainTable = 'insurances';

        $builder = $db->table($mainTable);
        $builder->select(array(
            $mainTable . '.id',
            $mainTable . '.insuranceStart',
            $mainTable . '.insuranceEnd',
            $mainTable . '.dateCancel',
            $mainTable . '.insurancePolisa',
            $mainTable . '.rateInsuranceDay',
            $mainTable . '.status',
        ));

        //$builder->where($mainTable.'.status', 2);
        $builder->where($mainTable . '.status !=', 3);
        //$builder->where($mainTable.'.seniorId', $jPFields['']);

        //----
        $join = array(
            'table1Field' => 'seniorId',
            'table2' => 'seniors', );

        $builder->join($join['table2'], $join['table2'] . '.id = ' . $mainTable . '.' . $join['table1Field'], 'left')
            ->select(array(
                $join['table2'] . '.id as seniorId',
                $join['table2'] . '.name as seniorName',
                $join['table2'] . '.surname as seniorSurname',
                $join['table2'] . '.city as seniorCity',
                //$join['table2'].'.corporationId as seniorCorporationId',
                //$join['table2'].'.corporationId as seniorCorporationId',
            ));

        //----

        //        //----

        $join['table2Field'] = 'corporationId';
        $join['table3'] = 'corporations';

        $builder->join($join['table3'], $join['table3'] . '.id = ' . $join['table2'] . '.' . $join['table2Field'], 'left')
            ->select(array(
                $join['table3'] . '.name as corporationName',
                //$join['table2'].'.surname as seniorSurname',
            ));

        //----

        //        //----

        $join['table5Field'] = 'insuranceFirmId';
        $join['table5'] = 'insuranceFirms';

        $builder->join($join['table5'], $join['table5'] . '.id = ' . $mainTable . '.' . $join['table5Field'], 'left')
            ->select(array(
                $join['table5'] . '.name as insuranceFirmName',
                //$join['table2'].'.surname as seniorSurname',
            ));

        //----

        //----

        $join['table3Field'] = 'workerId';
        $join['table4'] = 'workersSiud';

        $builder->join($join['table4'], $join['table4'] . '.id = ' . $mainTable . '.' . $join['table3Field'], 'left')
            ->select(array(
                $join['table4'] . '.name as workerName',
                $join['table4'] . '.surname as workerSurname',
                $join['table4'] . '.passport as workerPassport',
            ));

        //----

        $builder->where($mainTable . '.insuranceFirmId', $jPFields['insuranceId']);


        $builder->where($mainTable . '.insuranceEnd >= ', $startDate);


        $builder->orderBy($mainTable . '.insuranceEnd', 'ASC');

        $query = $builder->get();
        $results = $query->getResultArray();

        if (empty($results)) {
            return $results;
        } else {

            $results = $this->countDays($results, $startDate, $endDate);
        }


        return $results;

    }


    private function countDays($results, $startDate, $lastDayMonth)
    {


        $returnResults = array();

        //month: 01

        $startDateCalc = $startDate; //dont changes at foreEach;

        foreach ($results as $value) {


            //print_r($value);
            //die('-End');


            //            if(!empty($value['dateCancel'])) {
//                
//                 $value['insuranceEnd'] = $value['dateCancel'];
//                
//            }

            $date1 = new \DateTime($value['insuranceEnd']);
            $date2 = new \DateTime($lastDayMonth);
            $date3 = new \DateTime($startDateCalc);
            $date4 = new \DateTime($value['insuranceStart']);


            //if($date1 >= $date3 ) {
            if (!empty($value['rateInsuranceDay'])) {

                if ($date1 >= $date2) {

                    $lastMonthDay = $lastDayMonth;

                } else {

                    $lastMonthDay = $value['insuranceEnd'];
                }

                if ($date4 >= $date3) {

                    $startDate = $value['insuranceStart'];

                } else {

                    $startDate = $startDateCalc;
                }



                $date1 = new \DateTime($startDate); //agregar "/" antes de DateTime para las funciones nativas de php
                $date2 = new \DateTime($lastMonthDay);
                $interval = $date1->diff($date2);

                $firstDayInsurance = new \DateTime($value['insuranceStart']);
                $lastInsuranceDay = new \DateTime($value['insuranceEnd']);
                $interval2 = $firstDayInsurance->diff($lastInsuranceDay);

                //echo $startDate.' - '.$lastMonthDay;                
                //die('a: '.$interval->days);                
                //if( ($reminder['daysReminder'] == $interval->days) && !$interval->invert ) {

                $value['daysMonthCount'] = $interval->days + 1;

                $value['allDaysCount'] = $interval2->days + 1;

                $returnResults[] = $value;


            }



        }

        return $returnResults;


    }



    public function getDataDaysAgentMoneyInsurance($jPFields = false)
    {



        $startDate = '20' . $jPFields['year'] . '-' . $jPFields['month'] . '-' . '1';
        $endDate = date("Y-m-t", strtotime($startDate));


        $db = \Config\Database::connect();
        $mainTable = 'insurances';

        $builder = $db->table($mainTable);
        $builder->select(array(
            $mainTable . '.id',
            $mainTable . '.created_at',
            $mainTable . '.dateRequest',
            $mainTable . '.insuranceStart',
            $mainTable . '.insuranceEnd',
            $mainTable . '.dateCancel',
            $mainTable . '.insurancePolisa',
            $mainTable . '.status',
            $mainTable . '.rateInsuranceDay',
            $mainTable . '.payType',
            $mainTable . '.total',
            $mainTable . '.harelBuy'
        ));



        $builder->groupStart();
        $builder->where($mainTable . '.status', 2);
        $builder->orWhere($mainTable . '.status', 0);
        $builder->orWhere($mainTable . '.status', 3); // 09/08/2023
        $builder->groupEnd();

        //----
        $join = array(
            'table1Field' => 'seniorId',
            'table2' => 'seniors', );

        $builder->join($join['table2'], $join['table2'] . '.id = ' . $mainTable . '.' . $join['table1Field'], 'left')
            ->select(array(
                $join['table2'] . '.CustomerID as seniorCustomerID',
                $join['table2'] . '.id as seniorId',
                $join['table2'] . '.name as seniorName',
                $join['table2'] . '.surname as seniorSurname',
                $join['table2'] . '.city as seniorCity',
                $join['table2'] . '.corporationId as seniorCorporationId',
                //$join['table2'].'.corporationId as seniorCorporationId',
            ));

   

        $join['table2Field'] = 'corporationId';
        $join['table3'] = 'corporations';

        $builder->join($join['table3'], $join['table3'] . '.id = ' . $join['table2'] . '.' . $join['table2Field'], 'left')
            ->select(array(
                $join['table3'] . '.name as corporationName',
                //$join['table2'].'.surname as seniorSurname',
            ));

  

        $join['table5Field'] = 'insuranceFirmId';
        $join['table5'] = 'insuranceFirms';

        $builder->join($join['table5'], $join['table5'] . '.id = ' . $mainTable . '.' . $join['table5Field'], 'left')
            ->select(array(
                $join['table5'] . '.name as insuranceFirmName',
                $join['table5'] . '.id as insuranceFirmId',
            ));


        $join['table3Field'] = 'workerId';
        $join['table4'] = 'workersSiud';

        $builder->join($join['table4'], $join['table4'] . '.id = ' . $mainTable . '.' . $join['table3Field'], 'left')
            ->select(array(
                $join['table4'] . '.name as workerName',
                $join['table4'] . '.surname as workerSurname',
                $join['table4'] . '.passport as workerPassport',
            ));


        $builder->where($join['table2'] . '.corporationId', $jPFields['corporationId']);

        //$builder->where( $mainTable.'.insuranceStart <= ', $startDate );
        //$builder->where( $mainTable.'.insuranceEnd >= ', $startDate );

        //$builder->where( $mainTable.'.insuranceStart >= ', $startDate );
        //$builder->where( $mainTable.'.insuranceEnd >= ', $startDate );
        //$builder->where( $mainTable.'.insuranceStart <= ', $endDate );

        $builder->groupStart();

        $builder->groupStart();

        $builder->groupStart();
        $builder->where($mainTable . '.monthConsideration', null);
        $builder->where($mainTable . '.dateRequest >= ', $startDate);
        $builder->where($mainTable . '.dateRequest <= ', $endDate);
        $builder->groupEnd();

        $builder->orGroupStart();
        $builder->where($mainTable . '.monthConsideration !=', null);
        $builder->where($mainTable . '.monthConsideration >= ', $startDate);
        $builder->where($mainTable . '.monthConsideration <= ', $endDate);
        $builder->groupEnd();

        $builder->groupEnd();

        // 2 daniel doing this in comment
        $builder->orGroupStart();
        $builder->where($mainTable . '.dateCancel !=', null);
        $builder->where($mainTable . '.dateCancel >=', $startDate);
        $builder->where($mainTable . '.dateCancel <=', $endDate);
        $builder->where($mainTable . '.insuranceEnd >= ', $startDate);
        // $builder->where($mainTable . '.insuranceEnd <= ', $endDate);
        $builder->groupEnd();

        //$builder->groupStart();
        //$builder->orGroupStart();
        $builder->groupEnd();


        //die($startDate.' - '.$endDate);

        //$builder->orderBy( $join['table2'].'.name','ASC');

        $builder->orderBy($join['table2'] . '.CustomerID', 'ASC');


        $query = $builder->get();
        $results = $query->getResultArray();

    

        if (empty($results)) {
            return $results;
        } else {
            $results = $this->countDays($results, $startDate, $endDate);
            $results = $this->calcAgentMoney($db, $results); //AND DATE CANCEL REPLACE CHECK!!!
            $results = $this->fixCanceledInsurances($results, $startDate, $endDate); //AND DATE CANCEL REPLACE CHECK!!!
        }

        return $results;

    }

    private function fixCanceledInsurances($results, $startDate1, $endDate1)
    {

        $returnValues = array();

        foreach ($results as $value) {

            if ($value['status'] != '0') {
                $returnValues[] = $value;
                continue;
            }

            $dateCancel = new \DateTime($value['dateCancel']); //$value['insuranceEnd']
            $startDate = new \DateTime($startDate1);
            $endDate = new \DateTime($endDate1);
            //            
            $insuranceEnd = new \DateTime($value['insuranceEnd']);

            if ($value['id'] == '1090' and FALSE) {

                echo "<pre>";
                print_r($value);
                echo '</br></br>';

                echo 'dateCancel ' . $value['dateCancel'] . '</br></br>';
                echo 'startDate ' . $startDate1 . '</br></br>';

                if ($dateCancel >= $startDate) {
                    die('yes');
                }

                //                    if($dateCancel <= $endDate) {
//                        die('yes2');
//                    }

                die('TEST 1090');
            }

            //$dateCancel >= $startDate && //because not much to polisa 1090

            if (!empty($value['dateCancel']) && ($dateCancel <= $endDate)) {



                //double line: mebutal + mebutaj

                if (
                    $value['allDaysCount'] > 0 && (
                        //same month bitul
                        changeDateFormat($value['dateRequest'], "Y-m-d", $stFormatTo = "Ym") == changeDateFormat($value['dateCancel'], "Y-m-d", $stFormatTo = "Ym")
                    )
                ) {

                    $value['status'] = '2';
                    $returnValues[] = $value;

                    $value['status'] = '0';
                }

                //ביטול!!

                $intervalCancel = $dateCancel->diff($insuranceEnd);

                $value['allDaysCount'] = $intervalCancel->days + 1;


                $value['allDaysCount'] = 0 - $value['allDaysCount'];
                $value['totalAgent'] = ($value['allDaysCount'] * $value['rateInsuranceDay']);

                $number = 0 - ($value['totalAgent'] - $value['totalBasis']);
                $value['efresh'] = 0 - number_format((float) $number, 2, '.', '');


                $value['totalAgent'] = $value['totalAgent'];

                $value['dateRequestShow'] = changeDateFormat($value['dateCancel'], "Y-m-d  H:i", $stFormatTo = "d.m.Y");
                $value['insuranceStartShow'] = changeDateFormat($value['dateCancel'], "Y-m-d", $stFormatTo = "d.m.Y");


                $returnValues[] = $value;

            } else {

                $value['status'] = '2';
                $returnValues[] = $value;

                //echo "startDate: ".$startDate1.'<br/>';
                //echo "endDate: ".$endDate1.'<br/>';
                //die('dateCancel: '.$value['dateCancel']);







            }

            //


            //               {
//                    //$values['allDaysCount'] במינוס!!!
//                    //            מס' ימי הביטול X (תעריף יומי ששולם פחות תעריף יומי של הסוכן) = סכום העמלה במינוס.
//                    $value['totalAgent'] = 0 - $value['totalAgent'];
//                    
//                    $efreshCalc = $value['allDaysCount'] * ( $value['rateBasis'] - $value['rateInsuranceDay'] );
//                    
//                    $value['efresh'] = $efreshCalc;
//                
//                    
//                } 

        }

        return $returnValues;

    }

    private function calcAgentMoney($db, $results)
    {

        $returnValues = array();
        helper("site");



        foreach ($results as $value) {

            //            if(!empty($value['dateCancel'])) {
//                
//                $value['insuranceEnd'] = $value['dateCancel'];
//            }


            $value['dateRequestShow'] = changeDateFormat($value['dateRequest'], "Y-m-d  H:i", $stFormatTo = "d.m.Y");
            $value['created_atShow'] = changeDateFormat($value['created_at'], "Y-m-d  H:i", $stFormatTo = "d.m.Y");
            $value['insuranceStartShow'] = changeDateFormat($value['insuranceStart'], "Y-m-d", $stFormatTo = "d.m.Y");
            $value['insuranceEndShow'] = changeDateFormat($value['insuranceEnd'], "Y-m-d", $stFormatTo = "d.m.Y");


            if (empty($value['rateInsuranceDay'])) {

                $value['rateBasis'] = 0;
                $value['rateInsuranceDay'] = 0;

            } else {

                $mainTable = 'insuranceLinkPayDay';

                $rateBasis = $db->table($mainTable)
                    ->select(array(
                        $mainTable . '.id',
                        $mainTable . '.rateDay',

                    ))
                    ->where($mainTable . '.corporationId', $value['seniorCorporationId'])
                    ->where($mainTable . '.insuranceFirmId', $value['insuranceFirmId'])
                    ->get()
                    ->getRowArray();

                $value['rateBasis'] = isset($rateBasis['rateDay']) ? $rateBasis['rateDay'] : $value['rateInsuranceDay'];

            }



            $number = $value['allDaysCount'] * $value['rateBasis']; //daysMonthCount
            $value['totalBasis'] = number_format((float) $number, 2, '.', '');

            $number = $value['allDaysCount'] * $value['rateInsuranceDay'];   //daysMonthCount          
            $value['totalAgent'] = number_format((float) $number, 2, '.', '');

            $number = $value['totalAgent'] - $value['totalBasis'];
            $value['efresh'] = number_format((float) $number, 2, '.', '');

            //echo "<pre>";
            //print_r($value);
            //die();


            $payedFlag = false;

            if (!empty($value['total']) && !empty($value['payType'])) {

                $payedFlag = true;

            } else if (!empty($value['harelBuy'])) {

                $payedFlag = true;

            }

            $value['payed'] = $payedFlag;


            //                if( $value['status'] == '0' )  
//               {
//                    //$values['allDaysCount'] במינוס!!!
//                    //            מס' ימי הביטול X (תעריף יומי ששולם פחות תעריף יומי של הסוכן) = סכום העמלה במינוס.
//                    $value['totalAgent'] = 0 - $value['totalAgent'];
//                    
//                    $efreshCalc = $value['allDaysCount'] * ( $value['rateBasis'] - $value['rateInsuranceDay'] );
//                    
//                    $value['efresh'] = $efreshCalc;
//                
//                    
//                } 

            if ($value['status'] == '1') { // סטטוס חיתום

                $value['totalAgent'] = 0;
                $value['efresh'] = 0;

            }


            $returnValues[] = $value;

        }

        return $returnValues;



    }


    public function getDataUsersActivity($jPFields = false)
    {

        $db = \Config\Database::connect();

        $mainTable = "users";

        $users = $db->table($mainTable)
            ->select(array(
                $mainTable . '.id',
                $mainTable . '.name',
                $mainTable . '.corporationId',
                $mainTable . '.userType',

            ))
            ->where($mainTable . '.id !=', '8')
            ->where($mainTable . '.status', '1')
            ->groupStart()
            ->where($mainTable . '.corporationId !=', 'null')
            ->orWhere($mainTable . '.userType', '1')
            ->groupEnd()
            ->get()
            ->getResultArray();

        if (empty($users)) {
            return array();
        }


        $results = array();



        foreach ($users as $user) {

            $user['corpName'] = $this->getCorpName($db, $user);

            $user['insurances'] = $this->getInsurancesUser($user, $jPFields);

            $user['counters'] = $this->makeCounters($user['insurances'], $jPFields);

            $results[$user['id']] = $user;

        }

        $results['users'] = $users;

        //echo "<pre>";
        //print_r($results);
        //die('end');

        return $results;



    }

    private function makeCounters($userInsurances, $jPFields)
    {

        $startDate = '20' . $jPFields['year'] . '-' . $jPFields['month'] . '-' . '1';
        $endDate = date("Y-m-t", strtotime($startDate));

        $startDateObj = new \DateTime($startDate); //agregar "/" antes de DateTime para las funciones nativas de php
        $endDateObj = new \DateTime($endDate);

        $insurances = array();
        $insuranceDaysMonthCount = 0;

        $reNew = 0; //NEW AND RENEW
        $mustReNew = 0;

        if (!empty($userInsurances)) {


            foreach ($userInsurances as $insurance) {

                $insurances[$insurance['seniorId']][] = $insurance; //unique count array
                $insuranceDaysMonthCount = $insuranceDaysMonthCount + $insurance['daysMonthCount'];

                $insuranceStartDate = new \DateTime($insurance['insuranceStart']);
                $insuranceEndDate = new \DateTime($insurance['insuranceEnd']);

                //$insuranceStartDate = new \DateTime('2023-04-15');

                if ($insuranceStartDate >= $startDateObj && $insuranceStartDate <= $endDateObj) {

                    $reNew++;

                }

                if ($insuranceEndDate <= $endDateObj) { //must renew!

                    //search for exist renew
                    $existRenew = false;

                    foreach ($insurances[$insurance['seniorId']] as $insuranceSenior) {

                        $insuranceSeneniorEndDate = new \DateTime($insuranceSenior['insuranceEnd']);

                        if (
                            ($insuranceSenior['id'] != $insurance['id']) //not this insurance
                            && $insuranceSeneniorEndDate > $endDateObj
                        ) { //valid this month ?

                            $existRenew = true;

                        }

                    }

                    if (!$existRenew) {

                        $mustReNew++;

                    }

                }


            }
        }

        $results = array(
            'insureds' => count($insurances),
            'insuranceDaysMonthCount' => $insuranceDaysMonthCount,
            'reNew' => $reNew,
            'mustReNew' => $mustReNew
        );

        return $results;


    }



    private function getCorpName($db, $user)
    {

        if ($user['userType'] === '1') {

            return 'מנהל/ת מערכת';

        } else if (!empty($user['corporationId'])) {

            $corporation = $db->table('corporations')
                ->select('name')
                ->where('id', $user['corporationId'])
                ->get()->getRowArray();

            return isset($corporation['name']) ? $corporation['name'] : '-';

        } else {

            return '-';

        }


    }



    private function getInsurancesUser($user, $jPFields)
    {

        //        [month] => 03
//        [year] => 23


        $startDate = '20' . $jPFields['year'] . '-' . $jPFields['month'] . '-' . '1';
        $endDate = date("Y-m-t", strtotime($startDate));

        //die($endDate);

        $db = \Config\Database::connect();
        $mainTable = 'insurances';

        $builder = $db->table($mainTable);
        $builder->select(array(
            $mainTable . '.id',
            $mainTable . '.userId',
            $mainTable . '.insuranceStart',
            $mainTable . '.insuranceEnd',
            $mainTable . '.rateInsuranceDay',
            $mainTable . '.dateCancel',
        ));

        //----
        $join = array(
            'table1Field' => 'seniorId',
            'table2' => 'seniors', );

        $builder->join($join['table2'], $join['table2'] . '.id = ' . $mainTable . '.' . $join['table1Field'], 'left')
            ->select(array(
                $join['table2'] . '.id as seniorId',
            ));

        //----

        //        //----

        $join['table2Field'] = 'corporationId';
        $join['table3'] = 'corporations';

        $builder->join($join['table3'], $join['table3'] . '.id = ' . $join['table2'] . '.' . $join['table2Field'], 'left')
            ->select(array(
                $join['table3'] . '.name as corporationName',
                //$join['table2'].'.surname as seniorSurname',
            ));

        //----

        //----

        $join['table5Field'] = 'insuranceFirmId';
        $join['table5'] = 'insuranceFirms';

        $builder->join($join['table5'], $join['table5'] . '.id = ' . $mainTable . '.' . $join['table5Field'], 'left')
            ->select(array(
                $join['table5'] . '.name as insuranceFirmName',
                $join['table5'] . '.id as insuranceFirmId',
            ));

        //----

        //die('userId: '.$user['id']);

        //echo $user['id'];

        $builder->where($mainTable . '.userId', $user['id']);



        //        $builder->groupStart();
//        
//            $builder->where( $mainTable.'.insuranceStart <= ', $startDate );
//            
//            //OR WHERE THIS MONTH START
//            $builder->orGroupStart();
//                $builder->where( $mainTable.'.insuranceStart >= ', $startDate );
//                $builder->where( $mainTable.'.insuranceStart <= ', $endDate );
//            $builder->GroupEnd();
//        
//        $builder->groupEnd();

        $builder->where($mainTable . '.insuranceEnd >= ', $startDate);


        $builder->orderBy($mainTable . '.insuranceEnd', 'DESC');

        $query = $builder->get();
        $results = $query->getResultArray();

        if (empty($results)) {
            return $results;
        } else {

            $results = $this->countDays($results, $startDate, $endDate);
        }


        return $results;



    }



    public function getDataCorporationActivity($jPFields = false)
    {

        $startDate = '20' . $jPFields['year'] . '-' . $jPFields['month'] . '-' . $jPFields['day'] . ' 00:00';
        $endDate = '20' . $jPFields['year2'] . '-' . $jPFields['month2'] . '-' . $jPFields['day2'] . ' 23:59';


        $db = \Config\Database::connect();
        $mainTable = 'statusChangeLog';

        $builder = $db->table($mainTable);

        $builder->select(array(
            $mainTable . '.id as changesId',
            $mainTable . '.created_at',
            $mainTable . '.newStatusId',
        ));

        $builder->where($mainTable . ".created_at BETWEEN '$startDate' AND '$endDate'");

        $builder->where($mainTable . '.newStatusId', 2);

        $join = array(
            'table0Field' => 'insuranceId');

        $insurancesTable = 'insurances';

        $builder->join($insurancesTable, $insurancesTable . '.id = ' . $mainTable . '.' . $join['table0Field'], 'left')
            ->select(array(

                $insurancesTable . '.id statusChangeLogId',
                $insurancesTable . '.insuranceStart',
                $insurancesTable . '.insuranceEnd',
                $insurancesTable . '.status',
                $insurancesTable . '.kupatHolim',
                $insurancesTable . '.rateInsuranceDay',

            ));

        if (isset($jPFields['status'])) {

            if ($jPFields['status'] == '-1') {

                //$builder->where($insurancesTable.'.status !=', 3);

            } else {

                $builder->where($insurancesTable . '.status', $jPFields['status']);

            }



        } else {

            $builder->where($insurancesTable . '.status', 2);


        }

        $join = array(
            'table1Field' => 'seniorId',
            'table2' => 'seniors', );

        $builder->join($join['table2'], $join['table2'] . '.id = ' . $insurancesTable . '.' . $join['table1Field'], 'left')
            ->select(array(
                $join['table2'] . '.id as seniorId',
                $join['table2'] . '.name as seniorName',
                $join['table2'] . '.surname as seniorSurname',
                $join['table2'] . '.city as seniorCity',

                $join['table2'] . '.created_at as seniorCreated',
                $join['table2'] . '.updated_at as seniorUpdate',
                //$join['table2'].'.corporationId as seniorCorporationId',
                //$join['table2'].'.corporationId as seniorCorporationId',
            ));


        $join['table2Field'] = 'corporationId';
        $join['table3'] = 'corporations';

        $builder->join($join['table3'], $join['table3'] . '.id = ' . $join['table2'] . '.' . $join['table2Field'], 'left')
            ->select(array(
                $join['table3'] . '.name as corporationName',
                //$join['table2'].'.surname as seniorSurname',
            ));

    
        $join['table3Field'] = 'workerId';
        $join['table4'] = 'workersSiud';

        $builder->join($join['table4'], $join['table4'] . '.id = ' . $insurancesTable . '.' . $join['table3Field'], 'left')
            ->select(array(
                $join['table4'] . '.name as workerName',
                $join['table4'] . '.surname as workerSurname',

                $join['table4'] . '.created_at as workerCreated',
                $join['table4'] . '.updated_at as workerUpdate',
            ));


        $join['table4Field'] = 'insuranceFirmId';
        $join['table5'] = 'insuranceFirms';

        $builder->join($join['table5'], $join['table5'] . '.id = ' . $insurancesTable . '.' . $join['table4Field'], 'left')
            ->select(array(
                $join['table5'] . '.name as InsuranceFirmName'
            ));

        $join['table5Field'] = 'userId';
        $join['table6'] = 'users';

        $builder->join($join['table6'], $join['table6'] . '.id = ' . $mainTable . '.' . $join['table5Field'], 'left')
            ->select(array(
                $join['table6'] . '.name as userName'
            ));


        if ($jPFields['corporationId'] != '-1') {
            $builder->where($join['table2'] . '.corporationId', $jPFields['corporationId']);
        }

        if ($jPFields['selectedUserId'] != '-1') {
            $builder->where($join['table6'] . '.id', $jPFields['selectedUserId']);
        }

        if ($jPFields['selectedUserId'] != '8') {
            $builder->where($join['table6'] . '.id !=', 8);
        }

        //$builder->where($join['table2'].'.corporationId !=', '53'); //NO TEST!


        $builder->orderBy($mainTable . '.id', 'ASC');

        $query = $builder->get();
        $results = $query->getResultArray();

        return $results;

    }






}